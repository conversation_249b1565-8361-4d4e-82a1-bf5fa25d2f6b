import axios from 'axios';
import { message } from '@/utils/message';
import { authConfig } from '@/utils/config';

// 创建axios实例
const instance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么，例如添加token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    config.headers['systemcode'] = authConfig.systemCode;

    // 记录请求信息便于调试
    console.log(`[HTTP] ${config.method?.toUpperCase()} 请求: ${config.url}`, config.params || config.data);

    return config;
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const res = response.data;

    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return res;
    }

    // 登录失效处理
    if (res.code === 403 || res.code === 401) {
      // 清除本地token
      localStorage.removeItem('token')
      window.location.href = `${res.data.login_url}?callback=${window.location.href.split('#')[0]}`
      return Promise.reject(new Error('登录失效'))
    }

    // 特殊处理低代码转换服务的接口
    if (response.config.url?.includes('convert-to-lowcode')) {
      console.log('[HTTP] 低代码转换服务响应:', res);
      // 低代码转换服务的响应格式: { code: 0, message: 'success', data: {...} }
      if (res && res.message === 'success' && res.data) {
        return response;
      }
    }

    // 处理不同的API响应格式
    if (res.hasOwnProperty('success')) {
      // 格式1: { success: true, data: {...} }
      if (!res.success) {
        const errorMsg = res.message || '操作失败';
        message.error(errorMsg);
        return Promise.reject(new Error(errorMsg));
      }
      return res;
    } else if (res.hasOwnProperty('code')) {
      // 格式2: { code: 200, message: '', data: {...} }
      // 修改：允许 code=0 也表示成功
      if (res.code !== 200 && res.code !== 0) {
        const errorMsg = res.message || '操作失败';
        message.error(errorMsg);
        return Promise.reject(new Error(errorMsg));
      }
    }

    // 直接返回数据
    return response;
  },
  error => {
    // 对响应错误做点什么
    let errorMsg = '网络错误，请稍后重试';

    if (error.response) {
      const { status, data } = error.response;

      // 处理常见HTTP错误状态码
      switch (status) {
        case 400:
          errorMsg = data.message || '请求参数错误';
          break;
        case 401:
          errorMsg = '未授权，请重新登录';
          // 清除本地token
          localStorage.removeItem('token');

          // 重定向到登录页
          window.location.href = `${data.login_url}?callback=${window.location.href.split('#')[0]}`
          break;
        case 403:
          errorMsg = '拒绝访问';
          break;
        case 404:
          errorMsg = '请求的资源不存在';
          break;
        case 500:
          errorMsg = '服务器内部错误';
          break;
        default:
          errorMsg = data.message || `请求失败(${status})`;
      }
    } else if (error.request) {
      // 请求已发出但未收到响应
      errorMsg = '服务器无响应，请检查网络';
    } else {
      // 请求配置出错
      errorMsg = error.message;
    }

    // 显示错误消息
    message.error(errorMsg);

    return Promise.reject(error);
  }
);

export default instance;
