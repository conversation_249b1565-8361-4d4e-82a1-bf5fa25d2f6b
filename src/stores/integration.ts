import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Integration, IntegrationQuery, ChartConfig } from '@/types/integration';
import instance from '@/utils/axios';
import { ChartType, ChartTheme, ColumnAlign } from '@/types/integration';
import type { IntegrationConfig } from '@/types/integration/api-models';
import { integrationService } from '@/services/integrationService';
import type { 
  ExecuteQueryRequest, 
  QueryResult 
} from '@/types/integration';

// 直接定义IntegrationStatus类型而不是导入
type IntegrationStatus = 'DRAFT' | 'ACTIVE' | 'INACTIVE';

// 是否使用模拟数据 - 根据环境变量决定
const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';

// API基础URL从环境变量获取
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

// 日志开关-判断mock状态
console.log(`[集成Store] 初始化, API地址: ${API_BASE_URL}`);

export const useIntegrationStore = defineStore('integration', () => {
  // 状态
  const integrations = ref<Integration[]>([]);
  const currentIntegration = ref<Integration | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const lastPublishedPreviewUrl = ref<string | null>(null);
  const lastQueryResult = ref<{
    page: number;
    size: number;
    total: number;
    pages: number;
    first: boolean;
    last: boolean;
    hasNext: boolean;
    hasPrevious: boolean;
  } | null>(null);
  
  // 转换API响应到Integration类型
  const convertToIntegration = (data: IntegrationConfig): Integration => {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      type: data.type,
      status: data.status,
      queryId: data.queryId,
      dataSourceId: data.query?.dataSourceId,
      formConfig: data.config?.formConfig ? {
        layout: 'vertical',
        conditions: data.config.formConfig.fields.map(field => ({
          field: field.name,
          label: field.label,
          type: field.type as any,
          required: field.required,
          displayOrder: 0,
          visibility: 'visible'
        })),
        buttons: []
      } : undefined,
      tableConfig: data.config?.tableConfig ? {
        columns: data.config.tableConfig.columns.map(col => ({
          field: col.dataIndex,
          label: col.title,
          type: 'text',
          sortable: true,
          filterable: true,
          align: ColumnAlign.LEFT,
          visible: true,
          displayOrder: 0
        })),
        actions: [],
        pagination: {
          enabled: true,
          pageSize: 10,
          pageSizeOptions: [10, 20, 50]
        },
        export: {
          enabled: false,
          formats: [],
          maxRows: 1000
        },
        batchActions: [],
        aggregation: {
          enabled: false,
          groupByFields: [],
          aggregationFunctions: []
        },
        advancedFilters: {
          enabled: false,
          defaultFilters: [],
          savedFilters: []
        }
      } : undefined,
      chartConfig: data.config?.chartConfig ? {
        type: ChartType.LINE,
        title: data.config.chartConfig.type || '',
        description: '',
        theme: ChartTheme.LIGHT,
        height: 400,
        showLegend: true,
        animation: true,
        dataMapping: {
          xField: '',
          yField: '',
          seriesField: '',
          valueField: ''
        },
        styleOptions: {
          colors: [],
          backgroundColor: '#fff',
          fontFamily: 'Arial',
          borderRadius: 4,
          padding: [20, 20, 20, 20]
        },
        interactions: {
          enableZoom: false,
          enablePan: false,
          enableSelect: true,
          tooltipMode: 'single'
        }
      } : undefined,
      integrationPoint: {
        id: data.id,
        name: data.name,
        type: 'URL',
        urlConfig: {
          url: `${API_BASE_URL}/low-code/apis/${data.id}/query`,
          method: 'POST',
          headers: {}
        }
      },
      createTime: data.createdAt,
      updateTime: data.updatedAt
    };
  };

  // 获取集成列表
  const fetchIntegrations = async (params?: {
    page?: number;
    size?: number;
    name?: string;
    type?: 'SIMPLE_TABLE' | 'TABLE' | 'CHART';
    status?: IntegrationStatus;
  }) => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await integrationService.getIntegrations(params);
      console.log('[集成Store] 获取到集成数据:', result);
      
      if (result && result.items) {
        integrations.value = result.items;
        
        // 保存分页信息
        lastQueryResult.value = {
          page: result.page,
          size: result.size,
          total: result.total,
          pages: result.pages,
          first: result.first,
          last: result.last,
          hasNext: result.hasNext,
          hasPrevious: result.hasPrevious
        };
        
        return integrations.value;
      } else {
        console.error('[集成Store] 未知的API响应格式:', result);
        integrations.value = [];
        lastQueryResult.value = null;
        return [];
      }
    } catch (err: any) {
      console.error('获取集成列表失败', err);
      error.value = err.message || '获取集成列表失败';
      integrations.value = [];
      lastQueryResult.value = null;
      return [];
    } finally {
      loading.value = false;
    }
  };
  
  // 根据ID获取集成详情
  const fetchIntegrationById = async (id: string) => {
    loading.value = true;
    error.value = null;
    currentIntegration.value = null;
    
    try {
      console.log(`[集成Store] 开始获取集成详情 (ID: ${id})`);
      const integration = await integrationService.getIntegrationById(id);
      
      console.log('[集成Store] 获取到集成详情响应:', JSON.stringify(integration));
      console.log('[集成Store] 集成详情字段检查:', {
        hasQueryParams: Boolean(integration.queryParams),
        hasTableConfig: Boolean(integration.tableConfig),
        hasChartConfig: Boolean(integration.chartConfig),
        responseKeys: Object.keys(integration)
      });
      
      // 如果缺少关键配置字段，尝试从另一个地方获取
      if (integration && (!integration.queryParams || !integration.tableConfig)) {
        console.warn('[集成Store] 集成详情缺少关键配置字段，需要修复后端API返回完整数据');
        // 这里可以添加临时解决方案的逻辑，如从本地存储获取最近编辑的配置
      }
      
      currentIntegration.value = integration;
      return integration;
    } catch (err: any) {
      console.error(`获取集成详情失败 (ID: ${id})`, err);
      error.value = err.message || '获取集成详情失败';
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 创建集成
  const createIntegration = async (integration: {
    name: string;                             // 集成名称（必填）
    description?: string;                     // 描述信息
    type: 'SIMPLE_TABLE' | 'TABLE' | 'CHART'; // 集成类型（必填）
    queryId: string;                          // 关联的查询ID（必填）
    dataSourceId: string;                     // 数据源ID（必填）
    integrationPoint?: any;                   // 集成点配置
    chartConfig?: any;                        // 图表配置（CHART类型）
    tableConfig?: any;                        // 表格配置（TABLE类型）
    formConfig?: any;                         // 表单配置（FORM类型）
    params?: any[];                           // 查询参数列表
  }) => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await integrationService.createIntegration(integration);
      
      // 更新列表
      await fetchIntegrations();
      
      return result;
    } catch (err: any) {
      console.error('创建集成失败', err);
      error.value = err.message || '创建集成失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  // 更新集成
  const updateIntegration = async (id: string, integration: {
    name?: string;                             // 集成名称
    description?: string;                      // 描述信息
    type?: 'SIMPLE_TABLE' | 'TABLE' | 'CHART'; // 集成类型
    queryId?: string;                          // 关联的查询ID
    dataSourceId?: string;                     // 数据源ID
    integrationPoint?: any;                    // 集成点配置
    chartConfig?: any;                         // 图表配置（CHART类型）
    tableConfig?: any;                         // 表格配置（TABLE类型）
    formConfig?: any;                          // 表单配置（FORM类型）
    params?: any[];                            // 查询参数列表
  }) => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await integrationService.updateIntegration(id, integration);
      
      // 更新当前集成和列表
      if (currentIntegration.value && currentIntegration.value.id === id) {
        currentIntegration.value = result;
      }
      
      // 更新列表中的对应项
      const index = integrations.value.findIndex(item => item.id === id);
      if (index !== -1) {
        integrations.value[index] = result;
      }
      
      return result;
    } catch (err: any) {
      console.error(`更新集成失败 (ID: ${id})`, err);
      error.value = err.message || '更新集成失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  // 删除集成
  const deleteIntegration = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      await integrationService.deleteIntegration(id);
      
      // 如果当前集成是被删除的集成，清空它
      if (currentIntegration.value && currentIntegration.value.id === id) {
        currentIntegration.value = null;
      }
      
      // 从列表中移除
      integrations.value = integrations.value.filter(item => item.id !== id);
      
      return true;
    } catch (err: any) {
      console.error(`删除集成失败 (ID: ${id})`, err);
      error.value = err.message || '删除集成失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  // 执行集成查询
  const executeIntegrationQuery = async (request: ExecuteQueryRequest): Promise<QueryResult> => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await integrationService.executeQuery(request);
      return result;
    } catch (err: any) {
      console.error('执行集成查询失败', err);
      error.value = err.message || '执行集成查询失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  // 更新集成状态
  const updateIntegrationStatus = async (id: string, status: IntegrationStatus) => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await integrationService.updateIntegrationStatus(id, status);
      
      // 更新当前集成和列表
      if (currentIntegration.value && currentIntegration.value.id === id) {
        currentIntegration.value = result;
      }
      
      // 更新列表中的对应项
      const index = integrations.value.findIndex(item => item.id === id);
      if (index !== -1) {
        integrations.value[index] = result;
      }
      
      return result;
    } catch (err: any) {
      console.error(`更新集成状态失败 (ID: ${id}, 状态: ${status})`, err);
      error.value = err.message || '更新集成状态失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  // 预览集成
  const previewIntegration = async (id: string): Promise<QueryResult> => {
    loading.value = true;
    error.value = null;
    
    try {
      const result = await integrationService.previewIntegration(id);
      return result;
    } catch (err: any) {
      console.error(`预览集成失败 (ID: ${id})`, err);
      error.value = err.message || '预览集成失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  // 获取集成配置
  const getIntegrationConfig = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      // 调用接口获取配置
      const url = `${API_BASE_URL}/low-code/apis/${id}/config`;
      
      const requestOptions = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include' // 包含cookie
      };
      
      const response = await instance.get(url);
      return response.data;
    } catch (err: any) {
      console.error('获取集成配置失败', err);
      error.value = err.message || '获取集成配置失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  // 按类型过滤集成
  const filterByType = (type: string) => {
    return computed(() => {
      return integrations.value.filter(item => item.type === type);
    });
  };
  
  // 按状态过滤集成
  const filterByStatus = (status: IntegrationStatus) => {
    return computed(() => {
      return integrations.value.filter(item => item.status === status);
    });
  };
  
  // 搜索集成
  const searchIntegrations = (query: string) => {
    return computed(() => {
      if (!query) return integrations.value;
      
      const lowercaseQuery = query.toLowerCase();
      return integrations.value.filter(item => 
        item.name.toLowerCase().includes(lowercaseQuery) || 
        (item.description && item.description.toLowerCase().includes(lowercaseQuery))
      );
    });
  };
  
  return {
    // 状态
    integrations,
    currentIntegration,
    loading,
    error,
    lastQueryResult,
    lastPublishedPreviewUrl,
    
    // 操作方法
    fetchIntegrations,
    fetchIntegrationById,
    createIntegration,
    updateIntegration,
    deleteIntegration,
    executeIntegrationQuery,
    updateIntegrationStatus,
    previewIntegration,
    getIntegrationConfig,
    
    // 工具方法
    filterByType,
    filterByStatus,
    searchIntegrations
  };
});
