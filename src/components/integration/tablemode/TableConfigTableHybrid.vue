<template>
  <div class="bg-white shadow-sm sm:rounded-lg">
    <!-- 标签页切换 -->
    <div class="border-b border-gray-200">
      <nav class="flex" aria-label="Tabs">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          @click="activeTab = tab.key"
          class="px-4 py-3 font-medium text-sm border-b-2 focus:outline-none flex items-center"
          :class="[
            activeTab === tab.key
              ? 'border-indigo-500 text-indigo-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          <i :class="getTabIcon(tab.key)" class="mr-2"></i>
          {{ tab.label }}
        </button>
      </nav>
    </div>

    <!-- 查询结果配置 - 使用原始列配置UI -->
    <OriginalColumnsTab
      v-if="activeTab === 'columns'"
      v-model:columns="tableConfig.columns"
      :queryId="queryId"
      :queryVersionRef="queryVersionRef"
      @edit-column="editColumn"
    />

    <!-- 页面操作配置 - 使用新组件 -->
    <ActionsList
      v-if="activeTab === 'actions'"
      v-model:actions="tableConfig.actions"
      @edit="editAction"
    />

    <!-- 行操作配置 - 使用新组件 -->
    <RowActionsList
      v-if="activeTab === 'row-actions'"
      v-model:rowActions="tableConfig.rowActions"
      @edit="editRowAction"
    />

    <!-- 批量操作配置 - 使用新组件 -->
    <BatchActionsList
      v-if="activeTab === 'batch-actions'"
      v-model:batchActions="tableConfig.batchActions"
      @edit="onBatchActionEdit"
    />

    <!-- 表格特性配置 - 使用新组件 -->
    <FeaturesConfig
      v-if="activeTab === 'features'"
      v-model:pagination="tableConfig.pagination"
      v-model:exportConfig="tableConfig.export"
      v-model:rowActionsFixed="tableConfig.rowActionsFixed"
    />

    <!-- 操作按钮编辑弹窗 -->
    <ActionEditor
      v-if="showActionEditModal && editingAction"
      v-model="editingAction"
      @save="saveActionEdit"
      @close="closeActionEditModal"
    />

    <!-- 批量操作编辑弹窗 -->
    <BatchActionEditor
      v-if="showBatchActionEditModal && editingBatchAction"
      v-model="editingBatchAction"
      :index="editingBatchAction.index || 0"
      @save="saveBatchActionEdit"
      @close="closeBatchActionEditModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message } from '@/services/message';
import type { TableColumn, TableConfig, TableAction, BatchAction } from '@/types/integration';
import { ColumnDisplayType, ColumnAlign } from '@/types/integration';

// 导入原始列配置组件
import OriginalColumnsTab from './columnsTab/OriginalColumnsTab.vue';

// 导入新架构组件
import ActionsList from './actionsTab/ActionsList.vue';
import ActionEditor from './actionsTab/ActionEditor.vue';
import RowActionsList from './rowActionsTab/RowActionsList.vue';
import BatchActionsList from './batchActionsTab/BatchActionsList.vue';
import BatchActionEditor from './batchActionsTab/BatchActionEditor.vue';
import FeaturesConfig from './featuresTab/FeaturesConfig.vue';

// 辅助函数
import { getRecommendedDisplayType, getDefaultFormat, getDefaultAlign, getDefaultWidth } from '@/utils/typeMapping';
import { getApiBaseUrl } from "@/services/query";
import http from '@/utils/http';
import { extractFields } from '@/utils/responseAdapter';
import instance from '@/utils/axios';

// Props
const props = defineProps<{
  modelValue: TableConfig;
  queryId?: string;
  queryVersionRef?: any;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: TableConfig): void;
}>();

// 内部状态
const activeTab = ref('columns');
const showActionEditModal = ref(false);
const showBatchActionEditModal = ref(false);

// 编辑状态
const editingAction = ref<TableAction | undefined>(undefined);
const editingBatchAction = ref<BatchAction | undefined>(undefined);

// 标签页定义
const tabs = [
  { key: 'columns', label: '列配置' },
  { key: 'actions', label: '表格操作' },
  { key: 'row-actions', label: '行操作' },
  { key: 'batch-actions', label: '批量操作' },
  { key: 'features', label: '功能特性' }
];

// 表格配置，使用计算属性处理

// 防止类型错误的更安全方法
const tableConfig = computed(() => props.modelValue || {} as TableConfig);

// 监听表格配置变化并发出更新事件
function updateTableConfig(newConfig: TableConfig) {
  console.log('[TableConfigTableHybrid] 设置tableConfig', newConfig);
  // 使用深拷贝来避免引用问题，确保触发完整的响应式更新
  const newValue = JSON.parse(JSON.stringify(newConfig));
  emit('update:modelValue', newValue);
}

// 监听columns变化，确保视图更新
import { nextTick } from 'vue';
watch(() => tableConfig.value?.columns, (newColumns: any, oldColumns: any) => {
  // 日志记录当前列状态
  console.log('[TableConfigTableHybrid] 监听到columns变化, 新columns:', JSON.parse(JSON.stringify(newColumns)));
  
  if (newColumns && oldColumns && newColumns.length !== oldColumns.length) {
    console.log('[TableConfigTableHybrid] columns长度变化:', oldColumns.length, '->', newColumns.length);
    // 强制更新整个tableConfig以确保视图刷新
    nextTick(() => {
      const updatedConfig = JSON.parse(JSON.stringify(tableConfig.value));
      updateTableConfig(updatedConfig);
    });
  } else if (newColumns && oldColumns) {
    // 即使列数量没变，也深度检查config属性的变化
    console.log('[TableConfigTableHybrid] columns内容变化检查');
    
    // 检查是否有配置变化
    let hasConfigChanges = false;
    
    for (let i = 0; i < newColumns.length; i++) {
      const newConfig = JSON.stringify(newColumns[i].config || {});
      const oldConfig = JSON.stringify(oldColumns[i].config || {});
      
      if (newConfig !== oldConfig) {
        console.log(`[TableConfigTableHybrid] 列配置变化 [索引 ${i}]:`, 
          '\n旧config:', oldColumns[i].config, 
          '\n新config:', newColumns[i].config);
        hasConfigChanges = true;
      }
    }
    
    // 如果有配置变化，强制更新整个tableConfig
    if (hasConfigChanges) {
      nextTick(() => {
        console.log('[TableConfigTableHybrid] 检测到config属性变化，强制更新整个配置');
        const updatedConfig = JSON.parse(JSON.stringify(tableConfig.value));
        updateTableConfig(updatedConfig);
      });
    }
  }
}, { deep: true } as any);

// 确保每次通过v-model改变props时触发响应式更新
watch(() => props.modelValue, (newValue) => {
  // 如果外部更新了modelValue，回调函数会被触发
  // 由于tableConfig是计算属性，它应该会自动将新值反映出来
  console.log('[TableConfigTableHybrid] props.modelValue变化');
}, { deep: true } as any);

// 获取标签页图标
const getTabIcon = (tabKey: string): string => {
  switch (tabKey) {
    case 'columns':
      return 'fas fa-columns text-indigo-500';
    case 'actions':
      return 'fas fa-mouse-pointer text-indigo-500';
    case 'row-actions':
      return 'fas fa-edit text-indigo-500';
    case 'batch-actions':
      return 'fas fa-tasks text-indigo-500';
    case 'features':
      return 'fas fa-cog text-indigo-500';
    default:
      return 'fas fa-table text-indigo-500';
  }
};

// 确保组件挂载时检查配置并初始化默认值
onMounted(() => {
  console.log('[DEBUG] TableConfigTableHybrid 组件挂载，当前配置:', props.modelValue);

  // 如果modelValue为undefined或没有必要的子对象，初始化它们
  if (!props.modelValue || !props.modelValue.columns) {
    console.log('[DEBUG] 初始化默认表格配置');
    const defaultConfig: TableConfig = {
      columns: [],
      actions: [],
      rowActions: [], // 增加行操作列表
      pagination: {
        enabled: true,
        pageSize: 10,
        pageSizeOptions: [10, 20, 50, 100]
      },
      export: {
        enabled: true,
        formats: ['CSV', 'EXCEL'],
        maxRows: 1000
      },
      batchActions: [],
      aggregation: {
        enabled: false,
        groupByFields: [],
        aggregationFunctions: []
      },
      advancedFilters: {
        enabled: true,
        defaultFilters: [],
        savedFilters: []
      },
      rowActionsFixed: false
    };
    updateTableConfig(defaultConfig); // 使用updateTableConfig更新外部的modelValue
  }

  // 确保初始化rowActions
  if (tableConfig.value && !tableConfig.value.rowActions) {
    const updatedConfig = JSON.parse(JSON.stringify(tableConfig.value));
    updatedConfig.rowActions = [];
    updateTableConfig(updatedConfig);
  }

  // 确保列数据中每一列都有正确的isNewColumn标记
  if (tableConfig.value && tableConfig.value.columns && tableConfig.value.columns.length > 0) {
    console.log('[DEBUG] 标记现有列为非新增列');
    const updatedConfig = JSON.parse(JSON.stringify(tableConfig.value));
    updatedConfig.columns = updatedConfig.columns.map((column: any) => ({
      ...column,
      isNewColumn: false
    }));
    updateTableConfig(updatedConfig);
  }
});

// 列编辑相关方法
const editColumn = (column: TableColumn) => {
  console.log('[TableConfigTableHybrid] 编辑列:', column);
  
  // 深拷贝列对象确保config正确处理
  const deepCopyColumn = JSON.parse(JSON.stringify(column));
  console.log('[TableConfigTableHybrid] 编辑列(深拷贝后):', deepCopyColumn);
  
  // 当事件触发后，查找并更新tableConfig中对应的列
  const currentConfig = tableConfig.value as TableConfig;
  if (currentConfig && currentConfig.columns) {
    // 找到该列在columns中的索引
    const index = currentConfig.columns.findIndex(c => c.id === column.id);
    if (index !== -1) {
      // 创建更新后的配置对象
      const updatedConfig = JSON.parse(JSON.stringify(currentConfig));
      
      // 确保有完整的config复制
      updatedConfig.columns[index] = deepCopyColumn;
      
      console.log('[TableConfigTableHybrid] 列编辑后更新对应列:', deepCopyColumn);
      console.log('[TableConfigTableHybrid] 比较: 原列config:', currentConfig.columns[index].config, '新列config:', deepCopyColumn.config);
      
      // 将更新后的配置发送到父组件
      updateTableConfig(updatedConfig);
    }
  }
  
  // 此处可以实现额外的列编辑逻辑
};

// 操作按钮编辑
const editAction = (action: TableAction) => {
  // 创建一个深拷贝，防止直接修改原始对象
  editingAction.value = JSON.parse(JSON.stringify(action));
  // 存储原始标签用于在保存时定位
  if (editingAction.value) {
    editingAction.value.originalLabel = action.label;
  }
  showActionEditModal.value = true;
};

// 关闭操作按钮编辑弹窗
const closeActionEditModal = () => {
  showActionEditModal.value = false;
};

// 保存操作按钮编辑
const saveActionEdit = () => {
  if (!editingAction.value) return;
  
  // 将tableConfig的对象类型转换为TableConfig
  const currentConfig = tableConfig.value as TableConfig;
  if (!currentConfig || !currentConfig.actions) return;

  // 找到对应的操作按钮并更新
  const index = currentConfig.actions.findIndex((a: TableAction) =>
    a.label === editingAction.value?.originalLabel ||
    a.label === editingAction.value?.label
  );

  if (index !== -1 && editingAction.value) {
    // 创建配置的深拷贝
    const updatedConfig = JSON.parse(JSON.stringify(currentConfig));
    
    // 将编辑后的操作按钮应用回表格配置
    const { originalLabel, ...actionData } = editingAction.value;
    updatedConfig.actions[index] = { ...actionData };
    
    // 发送更新事件
    updateTableConfig(updatedConfig);
    message.success(`操作按钮 "${editingAction.value.label}" 配置已保存`);
  }

  closeActionEditModal();
};

// 编辑行操作按钮
const editRowAction = (action: TableAction) => {
  // 创建一个深拷贝，防止直接修改原始对象
  editingAction.value = JSON.parse(JSON.stringify(action));
  // 存储原始标签用于在保存时定位
  if (editingAction.value) {
    editingAction.value.originalLabel = action.label;
  }
  showActionEditModal.value = true;
};

// 批量操作编辑
const onBatchActionEdit = (action: BatchAction, index: number) => {
  // 深拷贝，避免直接修改源对象
  editingBatchAction.value = JSON.parse(JSON.stringify(action));
  // 存储原始ID和索引，用于保存时定位
  if (editingBatchAction.value) {
    editingBatchAction.value.originalId = action.id;
    editingBatchAction.value.index = index;
  }
  showBatchActionEditModal.value = true;
};

// 关闭批量操作编辑弹窗
const closeBatchActionEditModal = () => {
  showBatchActionEditModal.value = false;
};

// 保存批量操作编辑
const saveBatchActionEdit = () => {
  if (!editingBatchAction.value) return;

  // 将tableConfig的对象类型转换为TableConfig
  const currentConfig = tableConfig.value as TableConfig;
  if (!currentConfig || !currentConfig.batchActions) return;

  // 使用索引直接更新批量操作
  if (editingBatchAction.value.index !== undefined) {
    const index = editingBatchAction.value.index;
    // 移除临时属性
    const { originalId, index: idx, ...actionData } = editingBatchAction.value;
    
    if (currentConfig.batchActions[index]) {
      // 创建配置的深拷贝
      const updatedConfig = JSON.parse(JSON.stringify(currentConfig));
      updatedConfig.batchActions[index] = { ...actionData };
      
      // 发送更新事件
      updateTableConfig(updatedConfig);
      message.success(`批量操作 "${actionData.label}" 配置已更新`);
    }
  }
  showBatchActionEditModal.value = false;
};

// 转换为ColumnAlign类型
const convertToColumnAlign = (align: string): ColumnAlign => {
  switch (align) {
    case 'right': return ColumnAlign.RIGHT;
    case 'center': return ColumnAlign.CENTER;
    case 'left':
    default: return ColumnAlign.LEFT;
  }
};

// 从查询获取当前版本ID
const fetchCurrentVersionId = async (): Promise<string | undefined> => {
  try {
    // 如果有版本管理器引用，优先从它获取版本ID
    if (props.queryVersionRef && props.queryVersionRef.selectedVersionId) {
      const versionId = props.queryVersionRef.selectedVersionId;
      console.log(`[TableConfigTableHybrid] 从VersionManager获取到版本ID: ${versionId}`);
      return versionId;
    }

    // 如果没有版本管理器或无法获取，尝试查询API获取活跃版本
    if (props.queryId) {
      const baseUrl = getApiBaseUrl();
      const versionsUrl = `${baseUrl}/api/queries/${props.queryId}/versions`;
      console.log(`[TableConfigTableHybrid] 尝试从API获取版本列表: ${versionsUrl}`);

      // 添加请求头和请求选项
      const requestOptions = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include' // 包含cookie
      };

      const versionsResponse = await instance.get(versionsUrl);
      const versionsData = versionsResponse.data;
      if (versionsData.success && versionsData.data && versionsData.data.items) {
        // 查找活跃版本或最新版本
        const activeVersion = versionsData.data.items.find((v: any) =>
          v.status === 'ACTIVE' || v.isLatest
        );

        if (activeVersion) {
          console.log(`[TableConfigTableHybrid] 找到活跃版本: ${activeVersion.id}`);
          return activeVersion.id;
        }
      }
    }

    return undefined;
  } catch (error) {
    console.error('[TableConfigTableHybrid] 获取版本ID失败:', error);
    return undefined;
  } finally {
    // Empty finally block to fix syntax error
  }
};

// 为父组件暴露方法
defineExpose({
  importFieldsFromData: async () => {
    try {
      if (!props.queryId) {
        message.warning('无法导入字段: 必须选择查询才能导入字段，请先保存并指定查询配置');
        return;
      }

      console.log('[TableConfigTableHybrid] 开始从数据配置导入字段');
      message.info('正在从查询配置中获取可用字段，请稍候...');

      // 调用API获取字段列表
      const baseUrl = getApiBaseUrl();
      const url = `${baseUrl}/api/queries/${props.queryId}/parameters`;

      // 添加请求头和请求选项
      const requestOptions = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include' // 包含cookie
      };

      const response = await instance.get(url);
      const data = response.data;
      console.log('[TableConfigTableHybrid] 接收到字段数据:', data);

      // 使用响应适配器提取字段
      const fields = extractFields(data);
      console.log('[TableConfigTableHybrid] 通过适配器提取的字段列表:', fields);

      if (fields && fields.length > 0) {

        if (!fields.length) {
          message.warning('字段列表为空: 服务器返回了空的字段列表，请检查查询配置是否正确或已完成');
          return;
        }

        // 将tableConfig的对象类型转换为TableConfig
        const currentConfig = tableConfig.value as TableConfig;
        if (!currentConfig || !currentConfig.columns) {
          message.warning('表格列配置不存在，请先初始化配置');
          return;
        }
        
        // 获取已存在的字段列表，避免重复添加
        const existingFields = new Set(currentConfig.columns.map((col: TableColumn) => col.field));

        // 过滤出未添加的字段
        const newFields = fields.filter((field: any) => !existingFields.has(field.name));
        console.log('[TableConfigTableHybrid] 过滤出未添加的字段:', newFields);

        if (newFields.length === 0) {
          message.info('所有可用字段已导入表格配置中，无需重复导入');
          return;
        }

        // 转换并添加新列
        const newColumns = newFields.map((field: any, index: number) => {
          // 为新列创建唯一ID
          const displayOrder = currentConfig.columns.length + index;
          const uniqueId = `col_${Date.now()}_${index}`;

          // 根据字段类型设置列属性
          const fieldType = field.type || 'string';
          const format = getDefaultFormat(fieldType.toLowerCase());
          const align = convertToColumnAlign(getDefaultAlign(fieldType.toLowerCase()));

          // 根据format确定推荐的displayType
          const formatToDisplayTypeMap: Record<string, string> = {
            'string': 'TEXT',
            'int': 'NUMBER',
            'decimal': 'NUMBER',
            'enum': 'TAG',
            'date': 'DATE',
            'date-time': 'DATE',
            'card': 'SENSITIVITY',
            'mobile': 'SENSITIVITY',
            'uri': 'LINK',
            'email': 'SENSITIVITY',
            'json': 'TEXT',
            'boolean': 'STATUS'
          };

          // 根据format设置掩码类型
          const formatToMaskTypeMap: Record<string, string> = {
            'card': 'bankCard',
            'mobile': 'phone',
            'email': 'email',
            'name': 'name',
            'address': 'address',
            'fixPhone': 'fixPhone',
            'CVV': 'CVV',
            'idCard': 'idCard'
          };

          // 优先使用format映射的displayType，如果没有再使用字段类型推断
          const recommendedDisplayType = formatToDisplayTypeMap[format] || getRecommendedDisplayType(fieldType);

          // 根据format设置掩码类型
          let maskType = undefined;
          if (formatToMaskTypeMap[format]) {
            maskType = formatToMaskTypeMap[format];
            console.log(`[导入字段] 设置托码类型: format=${format}, maskType=${maskType}`);
          }

          // 检查字段是否为加密字段
          const isEncrypted = field.isEncrypted === true;
          console.log(`[导入字段] 字段${field.name}的加密状态:`, isEncrypted);

          // 创建基本列对象
          const baseColumnObj = {
            id: uniqueId, // 添加必需的id属性
            field: field.name,
            label: field.label || field.name,
            type: field.type || 'string',
            format: format,
            displayType: recommendedDisplayType as any, // 类型兼容处理
            width: getDefaultWidth(fieldType),
            align: align,
            visible: true,
            sortable: false,
            filterable: false,
            displayOrder: displayOrder,
            isNewColumn: false,
            maskType: maskType, // 添加掩码类型
            config: {
              help: "",
              fixedPoint: "2",
              thousandSeparator: true,
              truncate: false
            }
          };
          
          // 在TypeScript类型上不存在的扩展属性，使用类型断言
          const columnObj = {
            ...baseColumnObj,
            isEncrypted // 添加加密状态
          } as TableColumn;
          
          return columnObj;
        });

        // 添加新列到配置中
        const updatedConfig = JSON.parse(JSON.stringify(currentConfig));
        updatedConfig.columns = [...updatedConfig.columns, ...newColumns];
        
        // 发送更新事件
        updateTableConfig(updatedConfig);

        message.success(`成功导入 ${newColumns.length} 个字段: ${newColumns.slice(0, 3).map(c => c.label || c.field).join(', ')}${newColumns.length > 3 ? '...' : ''}`);
      } else {
        message.warning('服务器返回的数据结构不符合预期格式或没有可用字段，请检查API是否有变化');
      }
    } catch (error) {
      console.error('[TableConfigTableHybrid] 导入字段失败:', error);
      message.error(`导入字段失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
});
</script>
