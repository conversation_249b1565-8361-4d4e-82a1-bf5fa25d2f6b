<template>
  <div></div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message } from '@/services/message';
import { transformFrontendIntegrationToApi } from '@/utils/apiTransformer';
import { convertToStandardConfig } from '@/utils/configConverter';
import { syncTopLevelToConfig } from '@/utils/columnConfigSync';
import type { IntegrationData } from '@/types/unified-integration';

const props = defineProps<{
  integration: IntegrationData;
  queryParams: any[];
  tableConfig: any;
  chartConfig: any;
}>();

// 使用已导入的 message 服务

/**
 * 导出JSON配置
 */
const exportJson = () => {
  console.log('开始导出JSON...');

  // 添加详细的诊断信息
  console.log('[Export] 诊断信息 - 当前integration对象:', {
    id: props.integration.id,
    queryId: props.integration.queryId,
    versionId: props.integration.versionId,
    versionIdType: typeof props.integration.versionId,
    versionIdIsObject: typeof props.integration.versionId === 'object',
    versionIdEqualsQueryId: props.integration.versionId === props.integration.queryId
  });

  if (typeof props.integration.versionId === 'object') {
    console.log('[Export] versionId对象详情:', JSON.stringify(props.integration.versionId));
  }

  try {
    // 获取当前选择的queryId和versionId
    const queryId = props.integration.queryId || '';

    // 改进版本ID获取逻辑
    let versionId = '';

    // 如果versionId是对象（包含完整版本信息）
    if (typeof props.integration.versionId === 'object' && props.integration.versionId !== null) {
      // 从对象中获取ID
      versionId = props.integration.versionId.id || '';
      console.log('[Export] 从对象中获取版本ID:', versionId);
    }
    // 如果是字符串，直接使用
    else if (typeof props.integration.versionId === 'string' && props.integration.versionId) {
      versionId = props.integration.versionId;
      console.log('[Export] 使用字符串版本ID:', versionId);
    }
    // 如果没有版本ID，尝试从meta.apis中提取
    else if (props.integration.meta?.apis?.query?.path) {
      const apiPath = props.integration.meta.apis.query.path;
      console.log('[Export] 尝试从API路径提取版本ID:', apiPath);

      // 使用正则表达式从API路径中提取版本ID
      const versionRegex = /\/versions\/([^\/]+)\/execute/;
      const match = apiPath.match(versionRegex);

      if (match && match[1] && match[1] !== queryId) {
        versionId = match[1];
        console.log('[Export] 从API路径中提取到版本ID:', versionId);
      } else {
        console.warn('[Export] 无法从API路径中提取有效的版本ID');
        versionId = ''; // 清空版本ID，避免使用查询ID作为版本ID
      }
    }
    // 如果没有版本ID，输出警告日志
    else {
      console.warn('[Export] 未找到有效的版本ID，API请求可能无法正常工作');
      versionId = ''; // 清空版本ID，避免使用查询ID作为版本ID
    }

    // 安全检查：确保版本ID不等于查询ID（避免错误使用）
    if (versionId && versionId === queryId) {
      console.warn('[Export] 警告：版本ID与查询ID相同，这可能是一个错误');
    }

    // 当没有版本ID时通知用户
    if (!versionId) {
      console.warn('[Export] 导出配置时未找到版本ID');
      message.warning({
      content: '查询版本信息不完整',
      description: '未找到查询版本ID，将使用基本查询路径',
      duration: 4000
    });
    }

    // 检查网络请求中是否有实际的版本ID
    try {
      // 尝试从浏览器网络请求中查找最新的版本ID
      const findActualVersionId = () => {
        if (window.performance && window.performance.getEntriesByType) {
          const resources = window.performance.getEntriesByType('resource');

          for (const resource of resources) {
            const url = resource.name;
            // 查找包含execute的API请求
            if (url.includes('/queries/') && url.includes('/versions/') && url.includes('/execute')) {
              console.log('[Export] 找到可能的API请求:', url);

              // 使用正则表达式匹配版本ID
              const versionRegex = /\/versions\/([^\/]+)\/execute/;
              const match = url.match(versionRegex);

              if (match && match[1] && match[1] !== queryId) {
                const foundVersionId = match[1];
                console.log('[Export] 从网络请求中找到版本ID:', foundVersionId);
                return foundVersionId;
              }
            }
          }
        }
        return null;
      };

      // 尝试从网络请求中获取版本ID
      const actualVersionId = findActualVersionId();
      if (actualVersionId) {
        console.log('[Export] 使用从网络请求中发现的版本ID替换当前版本ID');
        versionId = actualVersionId;
      }
    } catch (e) {
      console.error('[Export] 尝试从网络请求中获取版本ID时出错:', e);
    }

    // 构建标准化的APIs定义
    const standardApis = {
      query: {
        method: 'POST',
        path: versionId
          ? `/data-scope/api/queries/${queryId}/versions/${versionId}/${props.integration.id}/execute-append`
          : `/data-scope/api/queries/${queryId}/${props.integration.id}/execute-append` // 如果没有版本ID，使用无版本的路径
      },
      download: {
        method: 'POST',
        path: versionId
          ? `/data-scope/api/excel/${queryId}/versions/${versionId}/${props.integration.id}/export`
          : `/data-scope/api/excel/${queryId}/${props.integration.id}/export`
      }
    };

    console.log('[Export] 生成标准化APIs定义:', standardApis);

    // 构建集成数据对象
    const integrationData: Record<string, any> = {
      id: props.integration.id,
      name: props.integration.name,
      description: props.integration.description,
      type: props.integration.type,
      status: props.integration.status,
      dataSourceId: props.integration.dataSourceId,
      queryId: props.integration.queryId,
      versionId: typeof props.integration.versionId === 'object'
        ? props.integration.versionId.id
        : props.integration.versionId,
      meta: {
        ...(props.integration.meta || {}),
        apis: standardApis
      }
    };

    console.log('[Export] 包含 meta.apis:', integrationData.meta.apis);

    // 添加查询参数
    integrationData.queryParams = props.queryParams;
    console.log(`添加查询参数，数量: ${props.queryParams.length}`);

    // 根据集成类型添加相应配置
    if (props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE') {
      console.log('添加表格配置');

      // 同步列配置，确保所有列的config属性都被正确设置
      if (props.tableConfig.columns && props.tableConfig.columns.length > 0) {
        console.log('开始同步列配置，数量:', props.tableConfig.columns.length);
        props.tableConfig.columns.forEach(column => {
          syncTopLevelToConfig(column);
          console.log(`同步列 ${column.field} 的配置成功，配置属性:`, column.config);
        });
      }

      // 确保tableConfig包含所有必要的属性
      integrationData.tableConfig = {
        ...props.tableConfig,
        // 如果tableConfig中没有显式包含rowActions，则将actions复制到rowActions
        rowActions: props.tableConfig.rowActions || props.tableConfig.actions || []
      };

      // 添加额外日志，用于诊断
      console.log('表格配置添加行操作按钮:');
      console.log('- tableConfig.actions数量:', props.tableConfig.actions?.length || 0);
      console.log('- tableConfig.actions内容:', JSON.stringify(props.tableConfig.actions || []));
      console.log('- 设置后的rowActions数量:', integrationData.tableConfig.rowActions?.length || 0);
      console.log('- 设置后的rowActions内容:', JSON.stringify(integrationData.tableConfig.rowActions || []));
    } else if (props.integration.type === 'CHART') {
      console.log('添加图表配置');
      integrationData.chartConfig = props.chartConfig;
      console.log('图表配置:', integrationData.chartConfig);
    }

    console.log('完整集成数据对象:', integrationData);
    console.log('集成对象中tableConfig的键:', integrationData.tableConfig ? Object.keys(integrationData.tableConfig) : '无');
    console.log('集成对象中的行操作按钮:', integrationData.tableConfig?.rowActions || '无');

    // 使用API转换器转换数据
    const transformedData = transformFrontendIntegrationToApi(integrationData);
    console.log('转换后的数据:', transformedData);
    console.log('转换后数据中的行操作按钮:', transformedData.tableConfig?.rowActions || '无');

    // 准备转换为标准格式的数据
    const standardInput = {
      meta: {
        database: integrationData.dataSourceId || '',
        schema: '',
        table: '',
        pageCode: typeof integrationData.id === 'string' ? integrationData.id : 'integration',
        // 包含 API 路径定义，确保使用标准化的APIs
        apis: standardApis
      },
      type: integrationData.type,
      queryParams: transformedData.queryParams || [],
      tableConfig: {
        ...transformedData.tableConfig,
        columns: transformedData.tableConfig?.columns || [],
        // 确保包含行操作按钮，优先使用已有的rowActions，如果没有则尝试使用actions
        rowActions: transformedData.tableConfig?.rowActions ||
                   transformedData.tableConfig?.actions || []
      },
      chartConfig: transformedData.chartConfig || null
    };

    console.log('准备转换为标准格式的数据:', standardInput);
    console.log('标准输入中的行操作来源检查:');
    console.log('- transformedData中的tableConfig.rowActions:',
                transformedData.tableConfig?.rowActions?.length || 0, '个');
    console.log('- transformedData中的tableConfig.actions:',
                transformedData.tableConfig?.actions?.length || 0, '个');
    console.log('- 最终使用的标准输入中的rowActions:',
                standardInput.tableConfig.rowActions?.length || 0, '个');

    // 转换为标准格式
    const standardConfig = convertToStandardConfig(standardInput);
    console.log('转换后的标准配置数据:', standardConfig);
    console.log('filter字段数量:', standardConfig.filter ? standardConfig.filter.length : 0);

    // 添加详细的filter字段日志
    if (standardConfig.filter && standardConfig.filter.length > 0) {
      console.log('过滤器详情:');
      standardConfig.filter.forEach((item: any, index: number) => {
        console.log(`  [${index}] key=${item.key}, displayType=${item.displayType}, isMultiValue=${item.config?.isMultiValue === true}, isSearchable=${item.config?.isSearchable === true}, enumKey=${item.config?.enumKey || 'none'}`);
      });
    }

    console.log('list字段数量:', standardConfig.list ? standardConfig.list.length : 0);
    console.log('operation.rowActions详情:');
    console.log('- 数量:', standardConfig.operation?.rowActions?.length || 0);
    console.log('- 内容:', JSON.stringify(standardConfig.operation?.rowActions || []));
    console.log('- 标准配置中的完整operation:', JSON.stringify(standardConfig.operation));

    // 转换为JSON字符串并导出
    const jsonString = JSON.stringify(standardConfig, null, 2);

    // 创建Blob对象并下载
    const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    const fileName = `integration-${props.integration.name || 'config'}-${new Date().toISOString().slice(0, 10)}.json`;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('JSON导出成功');
    message.success({
      content: 'JSON配置导出成功',
      description: '集成配置已成功导出为JSON文件',
      duration: 3000
    });
  } catch (error) {
    console.error('导出JSON时发生错误:', error);
    message.error({
      content: '导出JSON失败',
      description: '导出集成配置JSON时出现错误，请重试',
      duration: 5000
    });
  }
};

// 暴露方法给父组件
defineExpose({
  exportJson
});
</script>
