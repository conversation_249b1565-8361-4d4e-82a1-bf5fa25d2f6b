<script setup lang="ts">
// 定义组件属性
const props = defineProps({
  // 当前激活的标签页
  activeTab: {
    type: String,
    default: 'editor' // 'editor' | 'nlq' | 'builder'
  },
  // 当前查询是否正在执行
  isExecuting: {
    type: Boolean,
    default: false
  },
  // 是否可执行SQL查询
  canExecuteSQL: {
    type: Boolean,
    default: false
  },
  // 是否可执行自然语言查询
  canExecuteNLQ: {
    type: Boolean,
    default: false
  },
  // 是否可执行构建器查询
  canExecuteBuilder: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits<{
  (e: 'update:activeTab', tab: string): void
  (e: 'execute'): void
  (e: 'cancel'): void
}>()

// 切换标签页
const switchTab = (tab: string) => {
  if (props.isExecuting) return
  emit('update:activeTab', tab)
}

// 执行查询
const executeQuery = () => {
  emit('execute')
}

// 取消查询
const cancelQuery = () => {
  emit('cancel')
}

// 检查当前活动标签是否可执行查询
const canExecuteActive = () => {
  if (props.activeTab === 'editor') {
    return props.canExecuteSQL
  } else if (props.activeTab === 'nlq') {
    return props.canExecuteNLQ
  } else if (props.activeTab === 'builder') {
    return props.canExecuteBuilder
  }
  return false
}
</script>

<template>
  <div class="bg-white p-4 border-b border-gray-200">
    <div class="flex items-center justify-between">
      <!-- 标签页切换 -->
      <div class="flex space-x-1">
        <button
          @click="switchTab('editor')"
          class="px-4 py-2 text-sm font-medium rounded-md focus:outline-none"
          :class="activeTab === 'editor' 
            ? 'bg-indigo-100 text-indigo-700' 
            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'"
          :disabled="isExecuting"
        >
          <i class="fas fa-code mr-1"></i>
          SQL编辑器
        </button>
        
        <button
          @click="switchTab('nlq')"
          class="px-4 py-2 text-sm font-medium rounded-md focus:outline-none"
          :class="activeTab === 'nlq' 
            ? 'bg-indigo-100 text-indigo-700' 
            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'"
          :disabled="isExecuting"
        >
          <i class="fas fa-comment-alt mr-1"></i>
          自然语言查询
        </button>
        
        <button
          @click="switchTab('builder')"
          class="px-4 py-2 text-sm font-medium rounded-md focus:outline-none"
          :class="activeTab === 'builder' 
            ? 'bg-indigo-100 text-indigo-700' 
            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'"
          :disabled="isExecuting"
        >
          <i class="fas fa-th-large mr-1"></i>
          可视化构建器
        </button>
      </div>
      
      <!-- 执行/取消按钮 -->
      <div>
        <button
          v-if="!isExecuting"
          @click="executeQuery"
          class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <i class="fas fa-play mr-1"></i>
          执行查询
        </button>
        
        <button
          v-else
          @click="cancelQuery"
          class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          <i class="fas fa-stop mr-1"></i>
          取消查询
        </button>
      </div>
    </div>
  </div>
</template> 