<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { watch } from '@/plugins/vue-types-fix'
import { useDataSourceStore } from '@/stores/datasource'
// import { useSchemaStore } from '@/stores/schema'
// import { useDashboardStore } from '@/stores/dashboard'
import { message } from 'ant-design-vue'
import type { DataSource } from '@/types/datasource'
import type { SchemaMetadata } from '@/types/metadata'
import DataSourceSelector from '@/components/datasource/DataSourceSelector.vue'
import MetadataExplorer from '@/components/query/MetadataExplorer.vue'

// 组件属性定义
const props = defineProps<{
  dataSourceId?: string
}>()

// 事件定义
const emit = defineEmits<{
  (e: 'schemaChange', schema: string): void
  (e: 'dataSourceChange', dataSourceId: string): void
  (e: 'tableSelect', table: { name: string, [key: string]: any }): void
  (e: 'columnSelect', column: { name: string, [key: string]: any }, table: { name: string, [key: string]: any }): void
  (e: 'insertTable', tableName: string): void
  (e: 'insertColumn', columnName: string): void
}>()

// 状态管理
const dataSourceStore = useDataSourceStore()
// const schemaStore = useSchemaStore()
// const dashboardStore = useDashboardStore()
const metadataExplorerRef = ref<InstanceType<typeof MetadataExplorer> | null>(null)

// 数据源相关状态
const selectedDataSourceId = ref<string>('')
const selectedSchema = ref<string>('')
const schemaError = ref<string | null>(null)
const isLoadingSchemas = ref<boolean>(false)
const isRefreshingMetadata = ref<boolean>(false)

// 固定为元数据浏览面板
const leftPanel = ref<string>('metadata')

// 计算属性：获取当前选中的数据源
const selectedDataSource = computed(() => {
  if (!selectedDataSourceId.value) return null;
  
  // 只从已有的数据中获取数据源，不处理异步逻辑
  // 1. 首先从dataSources列表中查找
  const dataSourceFromList = dataSourceStore.dataSources.find(
    (ds: any) => ds.id === selectedDataSourceId.value
  );
  
  if (dataSourceFromList) {
    return dataSourceFromList;
  }
  
  // 2. 如果列表中没有，尝试从currentDataSource获取
  if (dataSourceStore.currentDataSource && dataSourceStore.currentDataSource.id === selectedDataSourceId.value) {
    return dataSourceStore.currentDataSource;
  }
  
  // 3. 都没找到则返回null
  return null;
})

// 计算属性：判断当前数据源是否可用
const isDataSourceActive = computed(() => {
  const dataSource = selectedDataSource.value;
  // 确保selectedDataSource.value存在且状态为active
  return Boolean(dataSource && dataSource.status === 'active');
})

// 计算属性：获取处理后的Schema列表
const processedSchemas = computed(() => {
  if (!selectedDataSourceId.value) return []
  
  // 使用dataSourceStore.metadataState.schemas代替不存在的getSchemasByDataSourceId方法
  const schemas = dataSourceStore.metadataState?.schemas?.get(selectedDataSourceId.value) || []
  
  // 调试日志 - 打印完整的原始数据，便于排查
  console.log('原始schemas数据(完整):', JSON.stringify(schemas));
  
  // 确保使用正确的ID字段作为value
  return schemas.map((schema: any) => {
    // 详细记录每个schema对象的结构，重点检查id字段
    console.log('Schema对象详情(完整):', schema);
    
    // 明确提取ID字段，优先使用schema.id作为value
    const schemaId = schema.id || schema.value;
    
    console.log(`处理schema: name=${schema.name}, id=${schemaId}`);
    
    return {
      name: schema.name || schema.value || schema,
      // 优先使用schema.id，这是真正的唯一标识符
      value: schemaId || schema.value || schema.name || schema,
      id: schemaId,
      tablesCount: schema.tablesCount
    };
  });
})

// 处理数据源变更
const handleDataSourceChange = (dataSourceId: string) => {
  try {
    if (dataSourceId === selectedDataSourceId.value) {
      console.log('相同的数据源ID，跳过重复加载');
      return;
    }
    
    // 更新选中的数据源ID并通知父组件
    console.log(`数据源ID从 ${selectedDataSourceId.value} 变为 ${dataSourceId}，强制清空schema`);
    selectedDataSourceId.value = dataSourceId;
    emit('dataSourceChange', dataSourceId);
    
    // 清空schema选择以及相关数据
    selectedSchema.value = '';
    schemaError.value = null;
    
    // 通知schema变更
    emit('schemaChange', '');
    
    // 通知元数据浏览器清空表数据
    console.log('数据源ID变化后，立即加载schemas');
    
    // 优先更新界面，然后异步加载数据
    nextTick(async () => {
      await loadSchemas();
    });
  } catch (error) {
    console.error('处理数据源变更时出错:', error);
  }
};

// 监听props.dataSourceId变化
watch(() => props.dataSourceId, (newVal: string | undefined) => {
  console.log('DataSourcePanel - props.dataSourceId变更:', newVal);
  
  // 只有当newVal存在且与当前选中值不同时才处理
  if (newVal && newVal !== selectedDataSourceId.value) {
    console.log('从props接收到新的数据源ID:', newVal);
    // 通过调用handleDataSourceChange来确保统一的处理逻辑
    handleDataSourceChange(newVal);
  }
}, { immediate: true });

// 监听数据源ID变化来加载schemas
watch(() => selectedDataSourceId.value, (newVal: string, oldVal: string) => {
  console.log(`数据源选择变更: ${oldVal} -> ${newVal}`);
  
  // 如果新值为空，清空schemas
  if (!newVal) {
    console.log('数据源ID为空，跳过加载schemas');
    return;
  }
  
  // 防止重复加载同一数据源
  if (newVal === oldVal) {
    console.log('数据源ID未变化，跳过重复加载');
    return;
  }
  
  // 异步加载schemas
  loadSchemas();
});

// Schema被选中时
const handleSchemaSelected = async () => {
  // 先检查是否有选中的Schema和数据源
  if (!selectedSchema.value || !selectedDataSourceId.value) {
    console.log('Schema或数据源ID为空，无法加载表信息')
    return
  }
  
  console.log('Schema被选中, 值:', selectedSchema.value)
  
  // 添加当前选中的schema的详细信息
  const selectedSchemaObj = processedSchemas.value.find((s: any) => s.value === selectedSchema.value);
  console.log('选中的schema详细信息:', selectedSchemaObj);
  
  // 清晰显示选择的是 ID 还是名称
  if (selectedSchemaObj) {
    console.log(`确认schema选择: 使用ID=${selectedSchema.value} 加载名称为 "${selectedSchemaObj.name}" 的schema表数据`);
  } else {
    console.warn(`未找到匹配的schema对象，直接使用值: ${selectedSchema.value}`);
  }
  
  // 触发schemaChange事件，通知父组件
  emit('schemaChange', selectedSchema.value)
  
  // 确保MetadataExplorer组件实例存在
  if (metadataExplorerRef.value) {
    // 显式调用加载表信息的方法，传递schemaId参数
    console.log('调用MetadataExplorer加载表信息, schemaID:', selectedSchema.value)
    
    try {
      // 显式记录传递的schemaId，以便调试
      console.log('传递到loadTables的schemaId:', selectedSchema.value)
      console.log('当前选中的数据源:', selectedDataSourceId.value)
      console.log('调用前确认schemaId类型:', typeof selectedSchema.value)
      
      // 确保传递的是schemaId而非数据源ID
      await metadataExplorerRef.value.loadTables(selectedSchema.value)
      console.log('成功加载表信息')
    } catch (error) {
      console.error('加载表信息失败:', error)
      message.error('加载表信息失败')
    }
  } else {
    console.warn('MetadataExplorer组件实例不存在，无法加载表信息')
  }
}

// 加载schemas
const loadSchemas = async () => {
  console.log('开始加载Schemas，数据源ID:', selectedDataSourceId.value);
  if (!selectedDataSourceId.value) {
    console.warn('无效的数据源ID，取消加载Schemas');
    return;
  }
  
  isLoadingSchemas.value = true;
  schemaError.value = null;
  
  let schemasLoadSuccess = false;
  
  try {
    // 尝试加载schemas
    console.log('调用dataSourceStore.getSchemas获取schemas数据');
    await dataSourceStore.getSchemas(selectedDataSourceId.value);
    schemasLoadSuccess = true;
    
    // 添加防护检查，确保processedSchemas.value存在并且是数组
    const schemas = processedSchemas.value || [];
    console.log('获取到的schemas:', schemas);
    
    if (schemas && schemas.length > 0) {
      // 详细记录第一个schema的信息
      const firstSchema = schemas[0];
      console.log('准备选择的第一个schema详情:', {
        name: firstSchema.name,
        value: firstSchema.value, // 这应该是schema的ID
        originalType: typeof firstSchema.value
      });
      
      // 明确记录选择的是ID还是名称
      console.log(`自动选择schema: ID=${firstSchema.value}, 名称=${firstSchema.name}`);
      
      // 如果有schema，选择第一个并触发schemaChange事件
      selectedSchema.value = firstSchema.value;
      
      // 确保schemaId有效后再触发事件
      if (firstSchema.value) {
        console.log('触发schemaChange事件:', firstSchema.value);
        emit('schemaChange', firstSchema.value);
        
        // 自动加载表数据
        if (metadataExplorerRef.value) {
          console.log('自动加载第一个schema的表数据');
          try {
            await metadataExplorerRef.value.loadTables(firstSchema.value);
          } catch (error) {
            console.error('自动加载表数据失败:', error);
          }
        }
      } else {
        console.warn('自动选择的schema ID无效:', firstSchema);
      }
    } else if (schemasLoadSuccess && metadataExplorerRef.value) {
      // 如果没有schema但schemas加载成功，尝试无schema加载表
      console.log('未发现schema，尝试无schema加载表');
      metadataExplorerRef.value.loadTablesWithoutSchema(selectedDataSourceId.value);
    }
  } catch (error: any) {
    // Schema加载失败
    schemasLoadSuccess = false;
    console.error('加载schema失败:', error);
    schemaError.value = error.message || '加载schema失败';
    message.error('加载schema失败: ' + (error.message || '未知错误'));
    // Schema加载失败，不执行后续的loadTablesWithoutSchema操作
  } finally {
    isLoadingSchemas.value = false;
    console.log('Schemas加载完成，成功状态:', schemasLoadSuccess);
  }
}

// 刷新元数据
const refreshMetadata = async () => {
  if (!selectedDataSourceId.value) return
  
  isRefreshingMetadata.value = true
  schemaError.value = null
  
  try {
    // 清除缓存
    dataSourceStore.clearMetadataCache(selectedDataSourceId.value)
    
    // 重新加载schemas
    await dataSourceStore.getSchemas(selectedDataSourceId.value)
    
    // 如果当前已选择schema，重新加载tables
    if (selectedSchema.value) {
      await dataSourceStore.getTables(selectedDataSourceId.value, selectedSchema.value)
    }
    
    message.success('元数据已刷新')
  } catch (err: any) {
    console.error('刷新元数据失败:', err)
    schemaError.value = err instanceof Error ? err.message : '刷新元数据失败'
    message.error('刷新元数据失败')
  } finally {
    isRefreshingMetadata.value = false
  }
}

// 处理元数据浏览器中的表格选择
const handleTableSelect = (table: {name: string, [key: string]: any}) => {
  console.log('选中表格:', table.name)
  emit('tableSelect', table)
}

// 处理元数据浏览器中的列选择
const handleColumnSelect = (column: {name: string, [key: string]: any}, table: {name: string, [key: string]: any}) => {
  console.log('选中列:', column.name, '表格:', table.name)
  emit('columnSelect', column, table)
}

// 向编辑器插入表名
const insertTableName = (tableName: string) => {
  console.log('插入表名:', tableName)
  emit('insertTable', tableName)
}

// 向编辑器插入列名
const insertColumnName = (columnName: string) => {
  console.log('插入列名:', columnName)
  emit('insertColumn', columnName)
}

// 加载数据
onMounted(() => {
  if (props.dataSourceId) {
    handleDataSourceChange(props.dataSourceId)
  }
})
</script>

<template>
  <div class="bg-white shadow rounded-lg" :data-current-datasource-id="selectedDataSourceId">
    <!-- 数据源选择区域 -->
    <div class="p-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">数据源</h3>
        <button 
          @click="refreshMetadata" 
          class="p-1 text-gray-500 hover:text-indigo-600 rounded"
          :class="{ 'animate-spin': isRefreshingMetadata }"
          :disabled="isRefreshingMetadata || !selectedDataSourceId"
          title="刷新元数据"
        >
          <i class="fas fa-sync-alt"></i>
        </button>
      </div>
      
      <!-- 使用DataSourceSelector组件 -->
      <div class="mt-2">
        <DataSourceSelector
          v-model="selectedDataSourceId"
          placeholder="请选择数据源"
          @selected="handleDataSourceChange"
        />
      </div>
      
      <!-- Schema选择器 -->
      <div class="mt-2" v-if="isDataSourceActive">
        <div class="relative">
          <select
            v-model="selectedSchema"
            class="appearance-none bg-white block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            :disabled="isLoadingSchemas"
            @change="handleSchemaSelected"
          >
            <option value="">{{ isLoadingSchemas ? '加载Schema中...' : '请选择Schema' }}</option>
            <option 
              v-for="schema in processedSchemas" 
              :key="schema.value" 
              :value="schema.value"
            >
              {{ schema.name }}
            </option>
          </select>
          <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        
        <!-- 加载状态和错误提示 -->
        <div v-if="schemaError" class="mt-2 text-sm text-red-600">
          <i class="fas fa-exclamation-circle mr-1"></i>
          {{ schemaError }}
        </div>
      </div>
    </div>
    
    <!-- 元数据浏览面板 -->
    <div v-if="leftPanel === 'metadata'" class="h-[calc(100vh-24rem)] overflow-y-auto">
      <!-- 判断是否选择了数据源 -->
      <div v-if="selectedDataSourceId">
        <!-- 判断所选数据源是否可用 -->
        <div v-if="selectedDataSource && selectedDataSource.status === 'active'">
          <!-- 这里是关键修改：无论是否选择了Schema都显示MetadataExplorer -->
          <MetadataExplorer
            ref="metadataExplorerRef"
            :dataSourceId="selectedDataSourceId"
            :schema="selectedSchema"
            :selectedSchema="selectedSchema"
            @table-select="handleTableSelect"
            @column-select="handleColumnSelect"
            @insert-table="insertTableName"
            @insert-column="insertColumnName"
            @noSchema="(hasNoSchema: boolean) => { console.log('数据源无Schema:', hasNoSchema) }"
          />
        </div>
        <!-- 数据源不可用时显示提示 -->
        <div v-else class="p-4 text-center">
          <div class="text-yellow-600 mb-2">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            选中的数据源当前不可用
          </div>
          <p class="text-gray-500 text-sm">
            该数据源状态为: {{ selectedDataSource ? selectedDataSource.status : '未知' }}
            <br>
            请选择一个可用的数据源或联系管理员解决问题
          </p>
        </div>
      </div>
      <!-- 未选择数据源时显示提示 -->
      <div v-else class="p-4 text-center text-gray-500">
        请先选择一个数据源
      </div>
    </div>
  </div>
</template>