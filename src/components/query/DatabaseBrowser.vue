<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useDataSourceStore } from '@/stores/datasource'
import { getMetadataApiUrl, getApiUrl } from '@/services/apiUtils'
import DataSourceSelector from './DataSourceSelector.vue'
import SchemaSelector from './SchemaSelector.vue'
import TableViewer from './TableViewer.vue'
import MetadataExplorer from './MetadataExplorer.vue'
import type { ExtendedTableMetadata, TableMetadata, ColumnMetadata } from '@/types/metadata'
import type { DataSource } from '@/types/datasource'
import { http } from '@/utils/http'
import instance from "@/utils/axios";

// 组件状态
const loading = ref(false)
const error = ref<{ type: string; message: string } | null>(null)
const currentDataSourceId = ref<string | undefined>(undefined)
const currentSchemaId = ref<string | undefined>(undefined)
const tables = ref<ExtendedTableMetadata[]>([])
const noSchema = ref(false)

// 数据源存储
const dataSourceStore = useDataSourceStore()

// 添加模板引用
const schemaSelectorRef = ref<InstanceType<typeof SchemaSelector> | null>(null)

// 子组件事件处理

// 处理数据源选择
const handleDataSourceSelected = async (dataSourceId: string) => {
  console.log('DatabaseBrowser - 数据源已选中:', dataSourceId)
  currentDataSourceId.value = dataSourceId
  // 清空其他状态
  currentSchemaId.value = undefined
  tables.value = []
  error.value = null
  noSchema.value = false

  // 直接调用SchemaSelector组件的方法加载schemas
  if (schemaSelectorRef.value) {
    await schemaSelectorRef.value.loadSchemasForDataSource(dataSourceId)
  }
}

// 处理Schema选择
const handleSchemaSelected = (schemaId: string) => {
  console.log('DatabaseBrowser - Schema已选中:', schemaId);
  currentSchemaId.value = schemaId;

  // 清空当前表格数据
  tables.value = [];

  if (currentDataSourceId.value && schemaId) {
    console.log(`DatabaseBrowser - 手动选择了schema: ${schemaId}，开始加载表结构`);
    loadTables(schemaId);
  }
}

// 处理MetadataExplorer组件中自动选择的schema
const handleAutoSchemaSelection = (schemaId: string) => {
  console.log('DatabaseBrowser - 收到自动选择的schema:', schemaId);
  
  // 更新当前schema
  currentSchemaId.value = schemaId;
  
  // 通知SchemaSelector组件更新选择
  if (schemaSelectorRef.value) {
    console.log(`DatabaseBrowser - 通知SchemaSelector组件更新选择为: ${schemaId}`);
    schemaSelectorRef.value.updateSelection(schemaId);
  }
  
  // 不在这里加载表，因为MetadataExplorer组件会自己处理表加载
}

// 处理Schema加载状态变化
const handleSchemaLoadingChange = (isLoading: boolean) => {
  loading.value = isLoading
}

// 处理Schema加载错误
const handleSchemaError = (err: { type: string, message: string }) => {
  error.value = err
}

// 处理无Schema情况
const handleNoSchema = () => {
  console.log('DatabaseBrowser - 数据源没有schema')
  noSchema.value = true

  // 自动加载表结构（无schema模式）
  if (currentDataSourceId.value) {
    loadTablesWithoutSchema(currentDataSourceId.value)
  }
}

// 处理表格加载完成
const handleTablesLoaded = (loadedTables: ExtendedTableMetadata[]) => {
  console.log(`DatabaseBrowser - 成功加载 ${loadedTables.length} 张表`)
  tables.value = loadedTables
}

// 加载表数据
const loadTables = async (schemaId: string) => {
  if (!schemaId) {
    console.error('DatabaseBrowser - loadTables: schemaId 为空，无法加载表数据')
    return
  }

  // 确保 schemaId 是有效的字符串
  if (typeof schemaId !== 'string' || schemaId === '{schemaId}') {
    console.error(`DatabaseBrowser - loadTables: 无效的 schemaId: ${schemaId}, 类型: ${typeof schemaId}`)
    error.value = {
      type: 'error',
      message: '无效的模式标识符，请重新选择架构'
    }
    return
  }

  console.log(`DatabaseBrowser - loadTables: 使用schemaID=${schemaId}加载表结构`)

  // 防止重复加载
  if (loading.value) {
    console.log('DatabaseBrowser - loadTables: 正在加载中，跳过重复请求')
    return
  }

  try {
    loading.value = true
    error.value = null

    // 尝试从store获取完整schema对象以确保有真正的ID
    if (currentDataSourceId.value) {
      const schemas = dataSourceStore.metadataState?.schemas?.get(currentDataSourceId.value) || []
      const schemaObj = schemas.find((s: any) => s.value === schemaId || s.id === schemaId || s.name === schemaId)

      if (schemaObj && schemaObj.id && schemaObj.id !== schemaId) {
        console.log(`DatabaseBrowser - loadTables: 找到完整schema对象，使用实际ID=${schemaObj.id}替代${schemaId}`)
        schemaId = schemaObj.id
      }
    }

    // 构建API URL
    const baseUrl = getApiUrl()
    const apiPrefix = baseUrl.endsWith('/api') ? '' : '/api'
    const apiUrl = `${baseUrl}${apiPrefix}/metadata/schemas/${schemaId}/tables`

    console.log(`DatabaseBrowser - loadTables: 加载表数据, URL: ${apiUrl}, schema ID: ${schemaId}`)

    const response = await instance.get(apiUrl)

    if (!response || response.status !== 200) {
      throw new Error(`加载表数据失败: ${response?.statusText || '未知错误'}`)
    }

    const data = response.data
    if (!data.success) {
      throw new Error('加载表数据失败')
    }

    // 处理表数据
    const loadedTables = Array.isArray(data.data) ?
      data.data.map((table: TableMetadata): ExtendedTableMetadata => ({
        ...table,
        isUpdating: false,
        lastUpdated: new Date().toISOString(),
        error: undefined
      })) : []

    console.log(`DatabaseBrowser - loadTables: 成功加载 ${loadedTables.length} 张表`)
    tables.value = loadedTables
  } catch (err) {
    console.error('DatabaseBrowser - loadTables: 加载表数据失败:', err)
    error.value = {
      type: 'error',
      message: err instanceof Error ? err.message : String(err)
    }
  } finally {
    loading.value = false
  }
}

// 加载无Schema情况下的表数据
const loadTablesWithoutSchema = async (dataSourceId: string) => {
  if (!dataSourceId) {
    console.error('DatabaseBrowser - loadTablesWithoutSchema: 数据源ID为空，无法加载表数据')
    return
  }

  // 首先检查是否已有缓存数据，避免重复请求
  const cachedTables = dataSourceStore.getTablesFromCache(dataSourceId)
  if (cachedTables && cachedTables.length > 0) {
    console.log(`DatabaseBrowser - loadTablesWithoutSchema: 使用缓存的表数据 (${cachedTables.length} 张表)`)

    tables.value = cachedTables.map((table: TableMetadata): ExtendedTableMetadata => ({
      ...table,
      isUpdating: false,
      lastUpdated: new Date().toISOString(),
      error: undefined
    }))

    return // 已有缓存，直接返回
  }

  // 防止重复请求
  if (loading.value) {
    console.log('DatabaseBrowser - loadTablesWithoutSchema: 正在加载中，跳过重复请求')
    return
  }

  try {
    loading.value = true
    error.value = null

    // 尝试获取当前数据源信息
    let schemaToUse = ''
    let dataSource = null

    try {
      // 首先尝试从dataSourceStore.dataSources中找到数据源
      dataSource = dataSourceStore.dataSources.find((ds: DataSource) => ds.id === dataSourceId)

      if (!dataSource) {
        // 如果在列表中没找到，尝试通过API获取数据源详情
        console.log('DatabaseBrowser - loadTablesWithoutSchema: 在列表中未找到数据源，尝试通过API获取')
        dataSource = await dataSourceStore.getDataSourceById(dataSourceId)
      }

      if (dataSource) {
        // 优先使用数据源的database字段
        if (dataSource.database) {
          schemaToUse = dataSource.database
          console.log(`DatabaseBrowser - loadTablesWithoutSchema: 使用数据源的database字段 "${schemaToUse}" 作为schemaId`)
        }
        // 其次尝试使用databaseName字段
        else if (dataSource.databaseName) {
          schemaToUse = dataSource.databaseName
          console.log(`DatabaseBrowser - loadTablesWithoutSchema: 使用数据源的databaseName字段 "${schemaToUse}" 作为schemaId`)
        }
        // 最后尝试使用name字段
        else if (dataSource.name) {
          schemaToUse = dataSource.name.toLowerCase().replace(/\s+/g, '_')
          console.log(`DatabaseBrowser - loadTablesWithoutSchema: 使用数据源名称派生的值 "${schemaToUse}" 作为schemaId`)
        }
      }

      // 如果所有尝试都失败，使用一个基于数据源ID的值
      if (!schemaToUse) {
        schemaToUse = `db_${dataSourceId.substring(0, 8)}`
        console.warn(`DatabaseBrowser - loadTablesWithoutSchema: 未能确定数据库名称，使用基于数据源ID的值 "${schemaToUse}"`)
      }
    } catch (err) {
      // 如果获取数据源信息失败，记录错误并使用数据源ID的一部分作为schemaId
      console.error('DatabaseBrowser - loadTablesWithoutSchema: 获取数据源详情失败:', err)
      schemaToUse = `db_${dataSourceId.substring(0, 8)}`
      console.warn(`DatabaseBrowser - loadTablesWithoutSchema: 获取数据源详情失败，使用基于ID的值 "${schemaToUse}"`)
    }

    // 构建API URL
    const baseUrl = getApiUrl()
    const apiPrefix = baseUrl.endsWith('/api') ? '' : '/api'
    const url = `${baseUrl}${apiPrefix}/metadata/schemas/${schemaToUse}/tables`
    console.log(`DatabaseBrowser - loadTablesWithoutSchema: 使用URL: ${url}, 原始schemaId:`, schemaToUse)

    const response = await instance.get(url)

    if (!response || response.status !== 200) {
      throw new Error(`获取表数据失败: ${response?.status || 'Unknown Error'} ${response?.statusText || ''}`)
    }

    const responseData = response.data

    if (!responseData.success) {
      throw new Error(responseData.message || '获取表数据失败')
    }

    const tablesData = responseData.data || []

    if (Array.isArray(tablesData) && tablesData.length > 0) {
      console.log(`DatabaseBrowser - loadTablesWithoutSchema: 成功获取到 ${tablesData.length} 张表`)

      tables.value = tablesData.map((table: TableMetadata): ExtendedTableMetadata => ({
        ...table,
        isUpdating: false,
        lastUpdated: new Date().toISOString(),
        error: undefined
      }))

      // 将表数据缓存到store
      if (typeof dataSourceStore.saveTablesToCache === 'function') {
        dataSourceStore.saveTablesToCache(dataSourceId, tablesData)
      }
    } else if (tablesData && !Array.isArray(tablesData)) {
      // 处理单个表的情况
      console.log(`DatabaseBrowser - loadTablesWithoutSchema: 获取到单个表`)
      tables.value = [{
        ...tablesData as TableMetadata,
        isUpdating: false,
        lastUpdated: new Date().toISOString(),
        error: undefined
      }]
    } else {
      console.warn('DatabaseBrowser - loadTablesWithoutSchema: 未获取到表数据')
      tables.value = []
      error.value = {
        type: 'warning',
        message: '未获取到表数据，数据源可能为空'
      }
    }
  } catch (err) {
    console.error('DatabaseBrowser - loadTablesWithoutSchema: 加载表数据出错:', err)
    error.value = {
      type: 'error',
      message: err instanceof Error ? err.message : String(err)
    }
  } finally {
    loading.value = false
  }
}

// 对外暴露方法
defineExpose({
  loadTables,
  loadTablesWithoutSchema
})

// 处理表格或列的选择事件 (如需要的话，可以添加此类事件处理)
// 这里先保留接口占位
const emit = defineEmits<{
  (e: 'table-select', table: TableMetadata): void
  (e: 'column-select', column: ColumnMetadata, table: TableMetadata): void
}>()
</script>

<template>
  <div class="database-browser h-full flex flex-col">
    <!-- 数据源选择 -->
    <div class="mb-3">
      <DataSourceSelector
        @data-source-selected="handleDataSourceSelected"
      />
    </div>

    <!-- Schema选择器 (如果有数据源) -->
    <div v-if="currentDataSourceId" class="mb-3">
      <SchemaSelector
        :dataSourceId="currentDataSourceId"
        :selectedSchema="currentSchemaId"
        @schema-selected="handleSchemaSelected"
        @loading-change="handleSchemaLoadingChange"
        @error="handleSchemaError"
        @no-schema="handleNoSchema"
        ref="schemaSelectorRef"
      />
    </div>

    <!-- 表结构展示 -->
    <div class="flex-1 relative">
      <!-- TableViewer组件 -->
      <TableViewer
        :tables="tables"
        :loading="loading"
        :error="error"
        :noSchema="noSchema"
        :dataSourceId="currentDataSourceId"
        :schemaId="currentSchemaId"
        @tables-loaded="handleTablesLoaded"
      />
      
      <!-- MetadataExplorer组件 (隐藏但用于自动处理) -->
      <div class="hidden">
        <MetadataExplorer 
          :dataSourceId="currentDataSourceId"
          :selectedSchema="currentSchemaId"
          :schema="currentSchemaId"
          @schema-selected="handleAutoSchemaSelection"
          @tablesLoaded="handleTablesLoaded"
          @noSchema="handleNoSchema"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.database-browser {
  width: 100%;
  display: flex;
  flex-direction: column;
}
</style>
