<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
import { ref, computed, onMounted, watch } from 'vue'
import { useDataSourceStore } from '@/stores/datasource'
import { dataSourceService } from '@/services/datasource'
import { message } from '@/services/message'
import type { DataSource, DataSourceType } from '@/types/datasource'

// 使用统一的消息服务

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  dataSourceType: {
    type: String as () => DataSourceType,
    default: undefined
  }
})

// 定义组件事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string | undefined): void
  (e: 'selected', id: string, dataSource: DataSource): void
  (e: 'change', id: string | undefined): void
}>()

// 组件状态
const loading = ref(false)
const dataSources = ref<DataSource[]>([])
const selectedDataSourceId = ref<string | undefined>(props.modelValue || '')
const searchValue = ref('')

// 计算属性：过滤后的数据源列表作为选项
const dataSourceOptions = computed(() => {
  let filtered = dataSources.value
  
  // 按类型过滤
  if (props.dataSourceType) {
    filtered = filtered.filter(ds => ds.type === props.dataSourceType)
  }
  
  return filtered.map(ds => ({
    value: ds.id,
    label: ds.name,
    disabled: ds.status !== 'active'
  }))
})

// 处理选择变更
const handleChange = (value: string) => {
  console.log('[DataSourceSelector] 数据源变更:', value)
  selectedDataSourceId.value = value
  
  // 立即更新modelValue并发出更新事件
  emit('update:modelValue', value)
  
  // 查找并发出选中的数据源对象
  if (value) {
    const selectedDs = dataSources.value.find(ds => ds.id === value)
    if (selectedDs) {
      console.log('[DataSourceSelector] 选中数据源:', selectedDs)
      // 确保先发出更新事件，再发出选中事件，以便父组件可以正确响应
      emit('change', value)
      emit('selected', value, selectedDs)
    } else {
      console.warn(`[DataSourceSelector] 无法找到ID为${value}的数据源`)
      // 即使找不到对象，也要发出变更事件
      emit('change', value)
    }
  } else {
    emit('change', undefined)
  }
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue: string | undefined) => {
  console.log(`[DataSourceSelector] modelValue变更: ${selectedDataSourceId.value} -> ${newValue}`)
  
  // 仅在值真正变化时更新
  if (selectedDataSourceId.value !== newValue) {
    selectedDataSourceId.value = newValue || undefined
    
    // 如果有新值但在当前数据源列表中不存在，尝试加载数据源
    if (newValue && dataSources.value.length > 0 && !dataSources.value.some(ds => ds.id === newValue)) {
      console.log(`[DataSourceSelector] 当前modelValue(${newValue})不在已加载的数据源列表中，重新加载`)
      loadDataSources()
    }
  }
}, { immediate: true })

// 生命周期钩子
onMounted(() => {
  console.log(`[DataSourceSelector] 组件挂载，初始modelValue: ${props.modelValue}`)
  loadDataSources()
})

// 添加加载状态管理
const isLoadingDataSources = ref(false)

// 加载数据源列表
const loadDataSources = async () => {
  if (isLoadingDataSources.value) {
    console.log('[DataSourceSelector] 正在加载中，跳过重复请求')
    return
  }

  loading.value = true
  isLoadingDataSources.value = true
  console.log('[DataSourceSelector] 开始加载数据源列表...')

  try {
    const result = await dataSourceService.getDataSources({ status: 'active' })
    
    console.log('[DataSourceSelector] 数据源服务返回结果:', result)
    
    if (result && result.items) {
      const oldLength = dataSources.value.length
      dataSources.value = result.items.map(dataSource => ({
        id: dataSource.id,
        name: dataSource.name || `数据源 ${dataSource.id}`,
        description: dataSource.description,
        type: dataSource.type,
        host: dataSource.host,
        port: dataSource.port,
        databaseName: dataSource.databaseName,
        database: dataSource.database,
        username: dataSource.username,
        status: dataSource.status,
        syncFrequency: dataSource.syncFrequency,
        lastSyncTime: dataSource.lastSyncTime,
        createdAt: dataSource.createdAt,
        updatedAt: dataSource.updatedAt
      }))
      
      console.log(`[DataSourceSelector] 成功加载${dataSources.value.length}个数据源(原${oldLength}个)`)
      
      // 打印完整的数据源列表，帮助调试
      dataSources.value.forEach((ds, index) => {
        console.log(`[DataSourceSelector] 数据源[${index}]: ID=${ds.id}, 名称=${ds.name}, 类型=${ds.type}, 状态=${ds.status}`)
      })
      
      // 如果有指定数据源类型，过滤并记录日志
      if (props.dataSourceType) {
        const filteredCount = dataSources.value.filter(ds => ds.type === props.dataSourceType).length
        console.log(`[DataSourceSelector] 按类型(${props.dataSourceType})过滤后剩余${filteredCount}个数据源`)
      }
    } else {
      console.warn('[DataSourceSelector] 数据源服务返回空结果')
    }
  } catch (error) {
    console.error('[DataSourceSelector] 加载数据源列表失败', error)
    message.error({
      content: '加载数据源列表失败',
      description: '无法从服务器获取数据源信息，请检查网络连接或稍后重试',
      duration: 5000
    })
  } finally {
    loading.value = false
  }
}

// 刷新数据源列表
const refreshDataSources = async () => {
  await loadDataSources()
}

// 过滤选项函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
</script>

<template>
  <div class="data-source-selector">
    <label 
      v-if="props.label" 
      :for="'data-source-selector-' + (Math.random().toString(36).substring(2))" 
      class="block text-sm font-medium text-gray-700 mb-1"
    >
      {{ props.label }}
      <span v-if="props.required" class="text-red-500">*</span>
    </label>
    
    <a-select
      v-model:value="selectedDataSourceId"
      :placeholder="props.placeholder || '请选择数据源'"
      :disabled="props.disabled"
      :loading="loading"
      show-search
      :filter-option="filterOption"
      :status="props.error ? 'error' : undefined"
      style="width: 100%"
      @change="handleChange"
      option-filter-prop="label"
      :options="dataSourceOptions"
    >
      <template #suffixIcon>
        <span class="suffix-icon-container">
          <i v-if="loading" class="fas fa-circle-notch fa-spin text-gray-400"></i>
          <a v-else @click.stop="refreshDataSources" title="刷新数据源列表">
            <i class="fas fa-sync-alt text-gray-400 hover:text-gray-600"></i>
          </a>
        </span>
      </template>
      <!-- 使用默认的a-select-option渲染方式，并自定义选项内容 -->
      <template #option="{ value, label, disabled }">
        <span class="flex items-center">
          <span 
            class="w-2 h-2 rounded-full mr-2"
            :class="{
              'bg-green-500': !disabled,
              'bg-gray-400': disabled,
            }"
          ></span>
          {{ label }}
        </span>
      </template>
    </a-select>
    
    <div v-if="props.error" class="mt-1 text-sm text-red-600">
      {{ props.error }}
    </div>
  </div>
</template>

<style scoped>
.data-source-selector {
  position: relative;
}

.suffix-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 4px;
}

:deep(.ant-select-selection-search-input) {
  height: 100%;
}

:deep(.ant-select-selector) {
  height: 38px !important;
  padding: 4px 11px !important;
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-select-selection-item) {
  line-height: 30px !important;
}
</style>